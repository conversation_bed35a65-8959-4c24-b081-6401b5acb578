/* Critical CSS - Above the fold styles only */
@import 'tailwindcss/base';

@theme {
  /* Essential color variables for critical rendering */
  --color-primary-50: #f5f3ff;
  --color-primary-100: #ede9fe;
  --color-primary-500: #8b5cf6;
  --color-primary-600: #7c3aed;
  --color-primary-700: #6d28d9;
  --color-primary-800: #5b21b6;
  --color-primary-900: #4c1d95;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;
  --color-secondary-950: #020617;

  --color-accent-400: #e6ee54;
  --color-accent-500: #d9e11a;

  --color-white: #fff;
  --color-black: #000;

  /* Essential typography */
  --font-sans: Inter, system-ui, sans-serif;
  --font-display: Lexend, system-ui, sans-serif;

  /* Essential spacing */
  --spacing: 0.25rem;
  --container-7xl: 80rem;

  /* Essential text sizes */
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* Essential font weights */
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Essential border radius */
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;

  /* Essential transitions */
  --default-transition-duration: 0.15s;
  --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

@custom-variant dark (&:is(.dark *));

/* Critical base styles */
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
    border-color: var(--color-secondary-200, currentcolor);
  }

  html {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--font-sans);
    -webkit-tap-highlight-color: transparent;
  }

  body {
    font-family: var(--font-sans);
    background-color: var(--color-white);
    color: var(--color-secondary-900);
    transition: background-color 0.3s, color 0.3s;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body:is(.dark *) {
    background-color: var(--color-secondary-950);
    color: var(--color-secondary-100);
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: var(--font-weight-bold);
  }

  h1 {
    font-size: var(--text-4xl);
    margin-bottom: calc(var(--spacing) * 6);
  }

  h2 {
    font-size: var(--text-3xl);
    margin-bottom: calc(var(--spacing) * 5);
  }

  a {
    color: inherit;
    text-decoration: inherit;
  }

  img {
    display: block;
    max-width: 100%;
    height: auto;
  }

  button {
    font: inherit;
    color: inherit;
    background-color: transparent;
    border-radius: 0;
  }
}

/* Critical utility classes for above-the-fold content */
.container-custom {
  width: 100%;
  max-width: var(--container-7xl);
  padding-inline: calc(var(--spacing) * 4);
  margin-inline: auto;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding-inline: calc(var(--spacing) * 6);
  padding-block: calc(var(--spacing) * 3);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--default-transition-duration) var(--default-transition-timing-function);
  cursor: pointer;
}

.btn-primary {
  background-color: var(--color-accent-400);
  color: var(--color-black);
}

.btn-primary:hover {
  background-color: var(--color-accent-500);
}

/* Critical layout utilities */
.flex { display: flex; }
.hidden { display: none; }
.block { display: block; }
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

/* Critical spacing */
.pt-20 { padding-top: calc(var(--spacing) * 20); }
.py-16 { padding-block: calc(var(--spacing) * 16); }
.mb-6 { margin-bottom: calc(var(--spacing) * 6); }
.mb-12 { margin-bottom: calc(var(--spacing) * 12); }

/* Critical text utilities */
.text-center { text-align: center; }
.text-white { color: var(--color-white); }
.text-4xl { font-size: var(--text-4xl); }
.text-lg { font-size: var(--text-lg); }
.font-bold { font-weight: var(--font-weight-bold); }

/* Critical background utilities */
.bg-white { background-color: var(--color-white); }
.bg-secondary-50 { background-color: var(--color-secondary-50); }
.bg-secondary-950 { background-color: var(--color-secondary-950); }

/* Dark mode variants for critical elements */
.bg-white:is(.dark *) { background-color: var(--color-secondary-950); }
.bg-secondary-50:is(.dark *) { background-color: var(--color-secondary-900); }
.text-secondary-900:is(.dark *) { color: var(--color-secondary-100); }

/* Critical responsive utilities */
@media (min-width: 48rem) {
  .md\:text-5xl { font-size: var(--text-5xl); }
  .md\:py-24 { padding-block: calc(var(--spacing) * 24); }
}

@media (min-width: 64rem) {
  .lg\:text-6xl { font-size: var(--text-6xl); }
  .container-custom { padding-inline: calc(var(--spacing) * 8); }
}

/* Critical transition utilities */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: var(--default-transition-timing-function);
  transition-duration: var(--default-transition-duration);
}

.duration-300 {
  transition-duration: 0.3s;
}
