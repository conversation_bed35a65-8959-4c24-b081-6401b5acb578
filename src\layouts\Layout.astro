---
import '../styles/transitions.css';
import { ViewTransitions } from 'astro:transitions';
import Header from '../components/Header.astro';
import PolishFooter from '../components/pl/Footer.astro';
import DefaultFooter from '../components/Footer.astro';
import SEO from '../components/SEO.astro';
import TrackingManager from '../components/TrackingManager.astro';
import CriticalCSS from '../components/CriticalCSS.astro';
import { getLangFromUrl } from '../i18n/utils';

export interface Props {
  title: string;
  description?: string;
  canonical?: string;
  image?: string;
  imageAlt?: string;
  type?: 'website' | 'article' | 'place';
  noindex?: boolean;
  nofollow?: boolean;
  structuredData?: object;
  Footer?: (props: any) => any;
  pageType?: string;
  enableTracking?: boolean;
  enableGA4?: boolean;
  enableGoogleAds?: boolean;
  enableAdSense?: boolean;
  enableCookieConsent?: boolean;
}

const {
  title,
  description = "Find and explore highway rest areas across Europe. Comprehensive directory with amenities, ratings, and detailed information for travelers.",
  canonical,
  image,
  imageAlt,
  type = 'website',
  noindex = false,
  nofollow = false,
  structuredData,
  Footer,
  pageType,
  enableTracking = true,
  enableGA4 = true,
  enableGoogleAds = true,
  enableAdSense = true,
  enableCookieConsent = true
} = Astro.props;

const currentLang = getLangFromUrl(Astro.url);
---

<!doctype html>
<html lang={currentLang} x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }" x-init="$watch('darkMode', val => localStorage.setItem('darkMode', val))" :class="{ 'dark': darkMode }">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />

    <!-- SEO Meta Tags -->
    <SEO
      title={title}
      description={description}
      canonical={canonical}
      image={image}
      imageAlt={imageAlt}
      type={type}
      noindex={noindex}
      nofollow={nofollow}
      structuredData={structuredData}
    />

    <!-- Critical CSS and optimized font loading -->
    <CriticalCSS />

    <!-- Tracking and Analytics -->
    {enableTracking && (
      <TrackingManager
        enableGA4={enableGA4}
        enableGoogleAds={enableGoogleAds}
        enableAdSense={enableAdSense}
        enableCookieConsent={enableCookieConsent}
        pageType={pageType}
      />
    )}

    <!-- View Transitions -->
    <ViewTransitions />
  </head>
  <body class="antialiased bg-white dark:bg-secondary-950 text-secondary-900 dark:text-secondary-100 transition-colors duration-300">
    <Header />
    <div id="page-wrapper" class="page-transition-wrapper pt-20">
      <slot />
    </div>
    {Footer ? <Footer /> : (currentLang === 'pl' ? <PolishFooter /> : <DefaultFooter />)}
    <script>
      // Optimized dark mode initialization
      if (localStorage.getItem('darkMode') === null) {
        // Check system preference
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          localStorage.setItem('darkMode', 'true');
          document.documentElement.classList.add('dark');
        } else {
          localStorage.setItem('darkMode', 'false');
        }
      }

      // Optimized page transitions with batched DOM operations
      document.addEventListener('astro:page-load', () => {
        // Use requestAnimationFrame to batch DOM operations
        requestAnimationFrame(() => {
          const pageWrapper = document.getElementById('page-wrapper');
          if (pageWrapper) {
            pageWrapper.classList.add('page-loaded');
          }
        });
      });

      document.addEventListener('astro:before-swap', () => {
        // Use requestAnimationFrame to batch DOM operations
        requestAnimationFrame(() => {
          const pageWrapper = document.getElementById('page-wrapper');
          if (pageWrapper) {
            pageWrapper.classList.remove('page-loaded');
          }
        });
      });
    </script>
    <script src="../scripts/transitions.js"></script>
  </body>
</html>
