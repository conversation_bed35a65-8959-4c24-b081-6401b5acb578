/* Non-critical CSS - Below the fold and interactive styles */
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@theme {
  /* Extended color palette */
  --color-primary-200: #ddd6fe;
  --color-primary-300: #c4b5fd;
  --color-primary-400: #a78bfa;
  --color-primary-950: #2e1065;

  --color-secondary-400: #94a3b8;

  --color-accent-50: #fcfdf5;
  --color-accent-100: #f8fadc;
  --color-accent-200: #f3f7b0;
  --color-accent-300: #eef484;
  --color-accent-600: #bbc40f;
  --color-accent-700: #99a10d;
  --color-accent-800: #7c8012;
  --color-accent-900: #676a14;
  --color-accent-950: #373b05;

  --color-warning-50: #fefce8;
  --color-warning-100: #fef9c3;
  --color-warning-200: #fef08a;
  --color-warning-300: #fde047;
  --color-warning-400: #facc15;
  --color-warning-500: #eab308;
  --color-warning-600: #ca8a04;
  --color-warning-700: #a16207;
  --color-warning-800: #854d0e;
  --color-warning-900: #713f12;
  --color-warning-950: #422006;

  /* Animation definitions */
  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.5s ease-in-out;
  --animate-slide-down: slideDown 0.5s ease-in-out;

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes slideUp {
    0% {
      transform: translateY(20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  @keyframes slideDown {
    0% {
      transform: translateY(-20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* Extended button utilities */
@utility btn-secondary {
  @apply btn bg-primary-600 hover:bg-primary-700 text-white shadow-md hover:shadow-lg;
}

@utility btn-accent {
  @apply btn bg-accent-500 hover:bg-accent-600 text-black shadow-md hover:shadow-lg;
}

@utility btn-outline {
  @apply btn border-2 border-secondary-300 dark:border-secondary-700 hover:bg-secondary-100 dark:hover:bg-secondary-800 text-secondary-900 dark:text-secondary-100;
}

/* Extended layout utilities */
@utility section {
  @apply py-16 md:py-24;
}

@utility content-section {
  @apply py-20 md:py-24;
}

@utility card {
  @apply bg-white dark:bg-secondary-900 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border border-secondary-200 dark:border-secondary-800;
}

/* Extended typography */
@layer base {
  h3 {
    @apply text-2xl md:text-3xl font-semibold mb-4;
  }

  h4 {
    @apply text-xl md:text-2xl font-semibold mb-4;
  }

  p {
    @apply text-base md:text-lg leading-relaxed mt-4 mb-6;
  }

  ul, ol {
    @apply mb-6;
  }
}

/* Animations */
.fade-in {
  @apply animate-fade-in;
}

.slide-up {
  @apply animate-slide-up;
}

.slide-down {
  @apply animate-slide-down;
}

/* Accessibility */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

/* Focus styles for keyboard navigation */
:focus-visible {
  @apply outline-hidden ring-2 ring-primary-500 dark:ring-primary-400;
}

/* Cursor management */
* {
  cursor: default !important;
}

/* Interactive elements should have pointer cursor */
a, button, [role="button"], input[type="submit"], input[type="button"], input[type="reset"],
select, .btn, .btn-primary, .btn-secondary, .btn-outline, .btn-accent,
[x-data] button, [x-data] select {
  cursor: pointer !important;
}

/* Text input elements should have text cursor */
input[type="text"], input[type="email"], input[type="password"], input[type="search"],
input[type="url"], input[type="tel"], input[type="number"], textarea {
  cursor: text !important;
}
