---
// Critical CSS component for inlining above-the-fold styles
import { readFileSync } from 'fs';
import { resolve } from 'path';

// Read critical CSS at build time
const criticalCSSPath = resolve(process.cwd(), 'src/styles/critical.css');
let criticalCSS = '';

try {
  criticalCSS = readFileSync(criticalCSSPath, 'utf-8');
  // Process the CSS to remove @import statements for inlining
  criticalCSS = criticalCSS.replace(/@import\s+[^;]+;/g, '');
} catch (error) {
  console.warn('Could not read critical CSS file:', error);
}
---

<!-- Inline critical CSS for immediate rendering -->
<style is:inline set:html={criticalCSS}></style>

<!-- Preload non-critical CSS and load it asynchronously -->
<link rel="preload" href="/src/styles/non-critical.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="/src/styles/non-critical.css"></noscript>

<!-- Preconnect to Google Fonts for faster font loading -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Load Google Fonts with font-display: swap for better performance -->
<link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Lexend:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Lexend:wght@400;500;600;700&display=swap"></noscript>

<script>
  // Polyfill for browsers that don't support rel="preload"
  (function() {
    const links = document.querySelectorAll('link[rel="preload"][as="style"]');
    links.forEach(link => {
      if (!link.onload) {
        link.onload = function() {
          this.onload = null;
          this.rel = 'stylesheet';
        };
      }
    });
  })();
</script>
